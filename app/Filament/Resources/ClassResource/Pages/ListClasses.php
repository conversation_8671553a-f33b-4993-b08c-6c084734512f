<?php

namespace App\Filament\Resources\ClassResource\Pages;

use App\Filament\Resources\ClassResource;
use App\Models\Classes;
use App\Services\GeneralSettingsService;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListClasses extends ListRecords
{
    protected static string $resource = ClassResource::class;

    protected function getHeaderActions(): array
    {
        $settingsService = app(GeneralSettingsService::class);
        $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
        $currentSemester = $settingsService->getCurrentSemester();
        $semesterText = $currentSemester == 1 ? '1st Semester' : '2nd Semester';

        return [
            Actions\CreateAction::make()
                ->icon('heroicon-o-plus')
                ->label('New Class'),

            Actions\Action::make('current_period_info')
                ->label("Showing: {$currentSchoolYear} - {$semesterText}")
                ->icon('heroicon-o-information-circle')
                ->color('info')
                ->disabled()
                ->tooltip('Classes are filtered to show only the current academic period. Use the tabs to filter by class type.'),
        ];
    }

    public function getTabs(): array
    {
        // Get current academic period info for display
        $settingsService = app(GeneralSettingsService::class);
        $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
        $currentSemester = $settingsService->getCurrentSemester();
        $semesterText = $currentSemester == 1 ? '1st Sem' : '2nd Sem';

        return [
            'all' => Tab::make('All Classes')
                ->badge(Classes::currentAcademicPeriod()->count())
                ->icon('heroicon-o-academic-cap')
                ->modifyQueryUsing(fn (Builder $query) => $query->currentAcademicPeriod())
                ->label("All Classes ({$currentSchoolYear} - {$semesterText})"),

            'college' => Tab::make('College Classes')
                ->badge(Classes::currentAcademicPeriod()->college()->count())
                ->icon('heroicon-o-building-library')
                ->modifyQueryUsing(fn (Builder $query) => $query->currentAcademicPeriod()->college())
                ->label("College ({$currentSchoolYear} - {$semesterText})"),

            'shs' => Tab::make('SHS Classes')
                ->badge(Classes::currentAcademicPeriod()->shs()->count())
                ->icon('heroicon-o-user-group')
                ->modifyQueryUsing(fn (Builder $query) => $query->currentAcademicPeriod()->shs())
                ->label("SHS ({$currentSchoolYear} - {$semesterText})"),
        ];
    }

    public function getTitle(): string
    {
        $settingsService = app(GeneralSettingsService::class);
        $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
        $currentSemester = $settingsService->getCurrentSemester();
        $semesterText = $currentSemester == 1 ? '1st Semester' : '2nd Semester';

        return "Classes - {$currentSchoolYear} ({$semesterText})";
    }
}
